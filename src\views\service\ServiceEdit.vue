
<template>
  <div class="service-edit">
    <TopNav :title="navTitle" :isBack="true" />
    <div class="page-main">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        class="service-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="form-section-title">基本信息</div>

          <el-form-item label="项目名称" prop="title">
            <el-input v-model="form.title" placeholder="请输入项目名称" style="width: 300px;" />
          </el-form-item>



          <el-form-item label="服务分类" prop="serviceCateId">
            <div class="category-selector-container">
              <el-select
                v-model="form.serviceCateId"
                placeholder="请选择服务分类"
                style="width: 300px;"
                filterable
                :loading="categoryLoading"
                clearable
                @change="handleCategoryChange"
                popper-class="category-select-dropdown"
              >
                <template #prefix v-if="form.serviceCateName">
                  <el-tag size="small" type="success" class="selected-category-tag">
                    {{ form.serviceCateName }}
                  </el-tag>
                </template>
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <div class="category-option">
                    <span>{{ item.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>
          </el-form-item>

          <el-form-item label="报价类型" prop="servicePriceType">
            <el-select
              v-model="form.servicePriceType"
              placeholder="请选择报价类型"
              style="width: 300px;"
            >
              <el-option label="一口价模式" :value="0" />
              <el-option label="报价模式" :value="1" />
              <el-option label="两者都有" :value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="服务价格" prop="price">
            <el-input-number
              v-model="form.price"
              :min="0"
              :precision="2"
              placeholder="请输入服务价格"
              style="width: 300px;"
            />
          </el-form-item>
          
          <el-form-item label="排序值" prop="top">
            <el-input-number
              v-model="form.top"
              :min="0"
              placeholder="请输入排序值"
              style="width: 300px;"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :value="1">可用</el-radio>
              <el-radio :value="-1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="资质" prop="qualification">
            <el-select
              v-model="form.qualification"
              placeholder="请选择资质"
              style="width: 300px;"
              clearable
              :loading="qualificationLoading"
            >
              <el-option
                v-for="(value, key) in qualificationOptions"
                :key="value"
                :label="key"
                :value="value"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 地区设置 -->
        <div class="form-section">
          <div class="form-section-title">地区设置</div>

          <el-form-item label="城市" prop="cityId">
            <div class="city-selector-container">
              <el-cascader
                v-model="form.cityId"
                :options="cityTreeData"
                :props="cascaderProps"
                placeholder="请选择省份和城市"
                style="width: 500px;"
                class="city-cascader"
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                :loading="cityLoading"
                @change="handleCityChange"
                popper-class="city-cascader-popper"
              >
                <template #default="{ node, data }">
                  <span>{{ data.name }}</span>
                  <span
                    v-if="data.children && data.children.length"
                    style="color: #999; font-size: 12px;"
                  >
                    ({{ data.children.length }}个城市)
                  </span>
                </template>
              </el-cascader>

              <div class="selected-cities-preview" v-if="selectedCityNames.length">
                <span class="city-count">已选择 {{ selectedCityNames.length }} 个城市</span>
                <el-tooltip placement="top" :content="selectedCityNames.join('、')">
                  <span class="city-names">{{ selectedCityNames.slice(0, 3).join('、') }}{{ selectedCityNames.length > 3 ? '...' : '' }}</span>
                </el-tooltip>
                <el-button
                  type="danger"
                  size="small"
                  plain
                      icon="Delete"
                      circle
                      @click="clearSelectedCities"
                      class="clear-cities-btn"
                    ></el-button>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="服务点" prop="agentIds">
                <el-select
                  v-model="form.agentIds"
                  placeholder="请输入关键词搜索"
                  style="width: 500px;"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="searchAgents"
                  :loading="agentLoading"
                  value-key="id"
                >
                  <el-option
                    v-for="item in agentOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
        </div>

        <!-- 内容设置 -->
        <div class="form-section">
              <div class="form-section-title">内容设置</div>

              <el-form-item label="封面图片" prop="cover">
                <el-upload
                  class="image-upload"
                  action="#"
                  :auto-upload="false"
                  :on-change="handleImageChange"
                  :on-remove="handleImageRemove"
                  :before-upload="beforeImageUpload"
                  :file-list="fileList"
                  list-type="picture-card"
                  :limit="1"
                  accept="image/*"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      只能上传jpg/png等图片文件，建议尺寸 750x400px
                    </div>
                  </template>
                </el-upload>

                <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
                  <el-progress :percentage="uploadProgress" :show-text="true" />
                  <p>上传中... {{ uploadProgress }}%</p>
                </div>
              </el-form-item>

              <el-form-item label="项目介绍" prop="introduce">
                <div class="editor-container">
                  <LbUeditor
                    v-model="form.introduce"
                    :height="'300px'"
                    placeholder="请输入项目介绍..."
                  />
                </div>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-section">
              <el-form-item>
                <LbButton type="primary" :loading="submitLoading" @click="handleSubmit" style="width: 120px;">
                  {{ isEdit ? '更新服务' : '新增服务' }}
                </LbButton>
                <LbButton @click="handleBack" style="margin-left: 12px; width: 80px;">
                  取消
                </LbButton>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElCard } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue' // Import Delete icon
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbUeditor from '@/components/common/LbUeditor.vue'
import { useEditOperations } from '@/composables/useDataRefresh'

// 导入API
import { api } from '@/api-v2'

const route = useRoute()
const router = useRouter()

// 使用编辑操作 Composable
const { handleAddSuccess, handleEditSuccess } = useEditOperations({
  module: 'service',
  router,
  redirectPath: '/service/list'
})

// 响应式数据
const submitLoading = ref(false)
const fileList = ref([])
const uploadProgress = ref(0)
const uploading = ref(false)
const categoryOptions = ref([])
const categoryLoading = ref(false)
const cityTreeData = ref([])
const cityLoading = ref(false)
const agentOptions = ref([])
const agentLoading = ref(false)
const navTitle = ref('')
const qualificationOptions = ref({})
const qualificationLoading = ref(false)

// 表单数据
const form = reactive({
  id: null,
  title: '',
  subTitle: '',
  cover: '',
  price: 0,
  serviceCateId: null,
  serviceCateName: '', // 添加分类名称字段用于显示
  servicePriceType: 0,
  top: 0,
  status: 1,
  qualification: null, // 添加资质字段
  introduce: '',
  explain: '',
  notice: '',
  cityId: [],
  cityStr: '',
  agentIds: []
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  serviceCateId: [
    { required: true, message: '请选择服务分类', trigger: 'change' }
  ],
  servicePriceType: [
    { required: true, message: '请选择报价类型', trigger: 'change' }
  ]
}

// 引用
const formRef = ref()

// 级联选择器配置
const cascaderProps = {
  value: 'cityId',
  label: 'name',
  children: 'children',
  leaf: 'leaf',
  checkStrictly: false,
  emitPath: true,
  multiple: true
}

// 计算属性
const isEdit = computed(() => route.query.type === 'edit' && !!route.query.id)

// 计算已选择的城市名称
const selectedCityNames = computed(() => {
  const names = []
  const getCityNames = (paths) => {
    if (!paths || !Array.isArray(paths) || paths.length === 0) return []

    paths.forEach(path => {
      if (Array.isArray(path) && path.length > 0) {
        // 获取最后一级的城市名称
        const findCityName = (treeData, pathIds) => {
          if (!treeData || !pathIds || pathIds.length === 0) return null

          let currentLevel = treeData
          let cityName = null

          // 遍历路径ID
          for (let i = 0; i < pathIds.length; i++) {
            const currentId = pathIds[i]
            const found = currentLevel.find(item => item.cityId === currentId)

            if (found) {
              // 如果是最后一级，保存城市名称
              if (i === pathIds.length - 1) {
                cityName = found.name
              }
              // 继续查找下一级
              currentLevel = found.children || []
            } else {
              break
            }
          }

          return cityName
        }

        const cityName = findCityName(cityTreeData.value, path)
        if (cityName) {
          names.push(cityName)
        }
      }
    })

    return names
  }

  return getCityNames(form.cityId)
})

// 方法
// 清空已选城市
const clearSelectedCities = () => {
  form.cityId = []
  form.cityStr = ''

  ElMessage({
    message: '已清空所有已选城市',
    type: 'info',
    duration: 2000
  })
}

// 返回处理
const handleBack = () => {
  router.push({ name: 'ServiceList' })
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 准备提交数据
    const submitData = {
      ...form,
      // 使用城市字符串格式
      cityId: form.cityStr || ''
    }

    console.log('📤 提交服务数据:', submitData)

    let result
    if (form.id) {
      // 编辑服务
      result = await api.service.serviceUpdate(submitData)
    } else {
      // 新增服务
      result = await api.service.serviceAdd(submitData)
    }

    if (result.code === '200') {
      // 使用统一的成功处理逻辑
      if (form.id) {
        handleEditSuccess(submitData, '更新成功')
      } else {
        handleAddSuccess(submitData, '新增成功')
      }
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    categoryLoading.value = true
    const result = await api.service.serviceCateList({
      status: 1, // 只获取启用的分类
      pageNum: 1,
      pageSize: 100 // 获取更多数据
    })
    console.log('📂 服务分类列表数据:', result)

    if (result.code === '200' || result.code === 200) {
      // 处理分页数据
      const categoryData = result.data.list || result.data || []
      console.log('📂 服务分类原始数据:', result)
      console.log('📂 处理后的分类数据:', categoryData)

      // 确保数据结构正确
      const formattedData = categoryData.map(item => ({
        id: item.id,
        name: item.name || item.categoryName || item.title,
        ...item
      }))

      categoryOptions.value = formattedData
      console.log('📂 格式化后的分类数据:', formattedData)

      // 验证数据结构
      if (formattedData.length > 0) {
        console.log('📂 第一个分类项:', formattedData[0])
        console.log('📂 分类选项总数:', formattedData.length)
      }

      // 如果没有数据，添加测试数据
      if (formattedData.length === 0) {
        console.warn('⚠️ 服务分类数据为空，添加测试数据')
        categoryOptions.value = [
          { id: 1, name: '测试分类1' },
          { id: 2, name: '测试分类2' },
          { id: 3, name: '测试分类3' }
        ]
      }
    } else {
      console.error('❌ 获取服务分类失败:', result)
      ElMessage.error(result.meg || result.msg || '获取服务分类失败')
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
    ElMessage.error('加载分类选项失败')
  } finally {
    categoryLoading.value = false
  }
}

// 加载城市树形数据
const loadCityTreeData = async () => {
  try {
    cityLoading.value = true
    const result = await api.technician.cityTree()
    console.log('🏙️ 城市树形数据:', result)

    if (result.code === 200 || result.code === '200') {
      cityTreeData.value = result.data || []
    } else {
      console.error('❌ 获取城市数据失败:', result)
      ElMessage.error(result.meg || result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('加载城市数据失败:', error)
    ElMessage.error('加载城市数据失败')
  } finally {
    cityLoading.value = false
  }
}

// 加载资质选项
const loadQualificationOptions = async () => {
  try {
    qualificationLoading.value = true
    const result = await api.service.qualificationList()
    console.log('🔍 资质列表数据:', result)

    if (result.code === '200') {
      qualificationOptions.value = result.data || {}
      console.log('📋 资质选项:', qualificationOptions.value)
    } else {
      console.error('❌ 获取资质列表失败:', result)
      ElMessage.error(result.meg || result.msg || '获取资质列表失败')
    }
  } catch (error) {
    console.error('加载资质选项失败:', error)
    ElMessage.error('加载资质选项失败')
  } finally {
    qualificationLoading.value = false
  }
}

// 服务分类选择变化处理
const handleCategoryChange = (value) => {
  console.log('📂 服务分类选择变化:', value)
  if (value) {
    const selectedCategory = categoryOptions.value.find(item => item.id === value)
    console.log('📂 选中的分类:', selectedCategory)

    // 保存分类名称到表单中，用于显示和提交
    if (selectedCategory) {
      form.serviceCateName = selectedCategory.name
      console.log('📂 已保存分类名称:', form.serviceCateName)

      // 显示选择成功的提示
      ElMessage({
        message: `已选择服务分类: ${selectedCategory.name}`,
        type: 'success',
        duration: 2000
      })
    }
  } else {
    // 清空分类名称
    form.serviceCateName = ''
  }
}

// 城市选择变化处理
const handleCityChange = (value) => {
  console.log('🔄 城市选择变化:', value)
  // 处理多选城市数据，转换为批量格式
  if (value && Array.isArray(value)) {
    // value 格式: [[省ID, 市ID], [省ID, 市ID], ...]
    console.log('🏙️ 选中的城市路径:', value)

    // 构建城市字符串，格式: "[[1,2],[19,20],[37,38]]"
    const cityStr = JSON.stringify(value)
    console.log('🏙️ 城市字符串:', cityStr)

    // 保存到表单中
    form.cityStr = cityStr

    // 记录选中的城市名称，用于显示
    console.log('🏙️ 已选择城市:', selectedCityNames.value)
  } else {
    form.cityStr = ''
  }
}

// 搜索服务点
const searchAgents = async (query) => {
  if (!query) {
    agentOptions.value = []
    return
  }

  try {
    agentLoading.value = true
    const result = await api.service.agentList({
      name: query,
      pageNum: 1,
      pageSize: 20
    })
    console.log('🔍 服务点搜索结果:', result)

    if (result.code === '200') {
      agentOptions.value = result.data.list || result.data || []
    } else {
      console.error('❌ 搜索服务点失败:', result)
      agentOptions.value = []
    }
  } catch (error) {
    console.error('搜索服务点失败:', error)
    agentOptions.value = []
  } finally {
    agentLoading.value = false
  }
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  console.log('📋 图片上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小 (例如，限制2MB)
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!');
    return false;
  }

  console.log('✅ 图片验证通过')
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fileList) => {
  console.log('🖼️ 图片文件变更:', file, fileList)

  // Only proceed if the file is new and valid, and we are not already uploading
  if (file.status === 'ready' && !uploading.value && file.raw) {
    // Make sure only one file exists in the fileList for single upload
    if (fileList.length > 1) {
      fileList.splice(0, fileList.length - 1); // Keep only the last one
    }
    await uploadImage(file)
  }
}

// 图片移除处理
const handleImageRemove = (file) => {
  console.log('🗑️ 移除图片:', file)
  form.cover = ''
  fileList.value = [] // Clear the file list completely
  uploadProgress.value = 0
}

// 执行图片上传
const uploadImage = async (file) => {
  console.log('📤 开始上传图片:', file)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      // 更新上传进度
      uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', uploadProgress.value + '%')
    })

    console.log('✅ 图片上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // 上传成功，保存文件URL到表单
      form.cover = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('图片上传成功')

      // Update fileList to reflect success state with correct URL
      fileList.value = [{
        name: file.name,
        url: form.cover,
        status: 'success'
      }];

      console.log('💾 图片URL已保存到表单:', form.cover)
    } else {
      throw new Error(result.meg || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))

    // 清理失败的文件
    fileList.value = []
    form.cover = ''
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// 加载编辑数据
const loadEditData = async () => {
  try {
    const result = await api.service.serviceInfo({ id: route.query.id })
    if (result.code === '200') {
      const data = result.data
      form.id = data.id
      form.title = data.title || ''
      form.subTitle = data.subTitle || ''
      form.cover = data.cover || ''
      form.price = data.price || 0
      form.serviceCateId = data.serviceCateId || null
      form.serviceCateName = data.serviceCateName || '' // 回填分类名称
      form.servicePriceType = data.servicePriceType || 0
      form.top = data.top || 0
      form.status = data.status || 1
      form.qualification = data.qualification || null // 回填资质数据
      form.introduce = data.introduce || ''
      form.explain = data.explain || ''
      form.notice = data.notice || ''

      // 处理城市数据回填
      if (data.cityId && typeof data.cityId === 'string') {
        try {
          // 如果是字符串格式 "[[1,2],[19,20]]"，解析为数组
          form.cityId = JSON.parse(data.cityId)
          form.cityStr = data.cityId
        } catch (e) {
          // If parsing fails, it might be a single city ID or invalid format
          console.warn('Invalid cityId string format, resetting cityId:', data.cityId);
          form.cityId = []
          form.cityStr = ''
        }
      } else if (Array.isArray(data.cityId)) { // If it's already an array
        form.cityId = data.cityId
        form.cityStr = JSON.stringify(data.cityId)
      } else {
        form.cityId = []
        form.cityStr = ''
      }

      form.agentIds = data.agentIds || []

      // 如果有封面图片，设置文件列表用于显示
      if (data.cover) {
        fileList.value = [{
          name: 'cover',
          url: data.cover,
          status: 'success'
        }]
      }
    } else {
      ElMessage.error(result.meg || '获取服务详情失败')
      return
    }
  } catch (error) {
    console.error('获取服务详情失败:', error)
    ElMessage.error('获取服务详情失败')
    return
  }
}

// 重置表单
const resetForm = () => {
  console.log('🔄 重置服务表单数据')

  form.id = null
  form.title = ''
  form.subTitle = ''
  form.cover = ''
  form.price = 0
  form.serviceCateId = null
  form.serviceCateName = '' // 重置分类名称
  form.servicePriceType = 0
  form.top = 0
  form.status = 1
  form.qualification = null // 重置资质字段
  form.introduce = ''
  form.explain = ''
  form.notice = ''
  form.cityId = []
  form.cityStr = ''
  form.agentIds = []
  fileList.value = []
  uploadProgress.value = 0
  uploading.value = false
  agentOptions.value = []

  // 清空表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听路由变化，处理新增/编辑模式切换
watch(() => route.query, (newQuery, oldQuery) => {
  // 更新导航标题
  navTitle.value = isEdit.value ? '编辑服务项目' : '新增服务项目'

  console.log('🔄 ServiceEdit 路由查询参数变化:', { newQuery, oldQuery })

  // 如果从编辑模式切换到新增模式，或者从其他页面进入新增模式，重置表单
  if ((oldQuery?.type === 'edit' && newQuery?.type === 'add') ||
      (newQuery?.type === 'add' && !oldQuery?.type)) {
    console.log('🔄 切换到新增模式，重置表单')
    resetForm()
  }

  // 如果是编辑模式且有ID，加载编辑数据
  if (newQuery?.type === 'edit' && newQuery?.id) {
    console.log('🔄 切换到编辑模式，加载数据:', newQuery.id)
    loadEditData()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('🚀 ServiceEdit 组件挂载，当前路由参数:', route.query)

  // 设置导航标题
  navTitle.value = isEdit.value ? '编辑服务项目' : '新增服务项目'

  // 加载基础数据
  loadCategoryOptions()
  loadCityTreeData()
  loadQualificationOptions()

  // 根据路由参数决定是新增还是编辑
  if (route.query.type === 'edit' && route.query.id) {
    console.log('📝 编辑模式，加载服务详情:', route.query.id)
    loadEditData()
  } else {
    console.log('➕ 新增模式，重置表单')
    resetForm()
  }
})
</script>

<style scoped>
.service-edit {
  /* 移除多余的内边距，避免与布局系统冲突 */
  padding: 0;
  /* 移除背景色，使用透明背景避免视觉层次混乱 */
  background-color: transparent;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.page-main {
  flex-grow: 1;
  display: flex;
  justify-content: center; /* Center the form card */
  /* 减少内边距，避免双重间距 */
  padding: 0;
}

.service-form-card {
  width: 100%;
  max-width: 960px; /* Max width for larger screens */
  /* 使用更简洁的边框样式，避免与 el-card 默认样式冲突 */
  border-radius: 8px;
  /* 使用轻微的阴影效果，避免过度的视觉层次 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  /* 确保卡片有干净的边框 */
  border: 1px solid #e4e7ed;
  /* 确保背景色为纯白，避免透明度问题 */
  background-color: #ffffff;
  /* 添加过渡效果 */
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

/* 卡片悬停效果 */
.service-form-card:hover {
  border-color: #d3d4d6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 覆盖 Element Plus Card 的默认样式 */
.service-form-card :deep(.el-card__body) {
  padding: 0; /* 移除默认内边距，由 .service-form 控制 */
}

/* Form layout improvements */
.service-form {
  /* 减少表单内边距，避免与卡片内边距重复 */
  padding: 24px;
}

.service-form :deep(.el-form-item) {
  margin-bottom: 24px; /* More vertical space between items */
}

.service-form :deep(.el-form-item__label) {
  font-weight: 600;
  color: #333; /* Darker label color */
  line-height: 32px; /* Adjust line height for better alignment */
  justify-content: flex-end; /* Align labels to the right */
  padding-right: 12px;
}

.service-form :deep(.el-input),
.service-form :deep(.el-input-number),
.service-form :deep(.el-select),
.service-form :deep(.el-cascader) {
  width: 100%;
}

/* Element Plus 输入框完整样式覆盖 - 解决双重边框问题 */

/* 强制重置所有可能的 input 元素 */
.service-form :deep(input),
.service-form :deep(input[type="text"]),
.service-form :deep(input[type="password"]),
.service-form :deep(input[type="email"]),
.service-form :deep(input[type="number"]),
.service-form :deep(input[type="tel"]),
.service-form :deep(input[type="url"]),
.service-form :deep(input[type="search"]),
.service-form :deep(input[type="date"]),
.service-form :deep(input[type="time"]),
.service-form :deep(input[type="datetime-local"]) {
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
  border-radius: 0 !important;
}

/* 外层容器样式 */
.service-form :deep(.el-input__wrapper),
.service-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
  box-shadow: none !important; /* 强制移除默认阴影 */
  background-color: #ffffff;
}

/* 输入框内部的原生 input 元素 - 移除所有边框和背景 */
.service-form :deep(.el-input__inner) {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0;
  margin: 0;
}

/* 焦点状态 */
.service-form :deep(.el-input__wrapper.is-focus),
.service-form :deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 确保焦点状态下内部 input 仍然无边框 */
.service-form :deep(.el-input__wrapper.is-focus .el-input__inner),
.service-form :deep(.el-input__wrapper:focus-within .el-input__inner) {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 悬停状态 */
.service-form :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

/* 禁用状态 */
.service-form :deep(.el-input__wrapper.is-disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.service-form :deep(.el-input__wrapper.is-disabled .el-input__inner) {
  background-color: transparent !important;
  cursor: not-allowed;
  color: #c0c4cc;
}

/* 数字输入框特殊处理 */
.service-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  box-shadow: none !important;
}

.service-form :deep(.el-input-number .el-input__inner) {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.service-form :deep(.el-radio-group) {
  line-height: 32px; /* Align with other form elements */
  height: 32px;
  display: flex; /* Use flex to align radios horizontally */
  align-items: center;
}

.service-form :deep(.el-radio) {
  margin-right: 20px; /* Space between radio buttons */
}

/* City Cascader specific styles */
.city-selector-container {
  width: 100%;
}

.city-cascader {
  min-height: 40px;
}

.city-cascader :deep(.el-cascader__tags) {
  min-height: 38px; /* Adjusted height for tags */
  max-height: 100px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 4px 8px; /* Padding inside the tags container */
}

.city-cascader :deep(.el-tag) {
  max-width: calc(33% - 8px); /* Adjust max-width for better display of tags */
  margin: 2px 4px 2px 0;
  font-size: 12px;
  height: 28px; /* Slightly taller tags */
  line-height: 26px;
  border-radius: 4px; /* More rounded tags */
  background-color: #ecf5ff; /* Light blue background */
  border-color: #d9ecff; /* Light blue border */
  color: #409eff; /* Blue text */
  transition: all 0.2s ease;
}

.city-cascader :deep(.el-tag .el-tag__content) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.city-cascader :deep(.el-tag .el-tag__close) {
  color: #409eff;
}

.city-cascader :deep(.el-tag:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #d9ecff;
  border-color: #a0cfff;
}

/* Cascader dropdown panel */
:deep(.city-cascader-popper) {
  max-height: 350px;
  overflow-y: auto;
  width: auto !important; /* Let content define width */
  min-width: 400px; /* Minimum width for the cascader dropdown */
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18) !important;
  border: none;
}

:deep(.city-cascader-popper .el-cascader-panel) {
  max-height: 320px;
  overflow-y: auto;
  padding: 0;
  border-right: 1px solid #ebeef5; /* Separator between panels */
}

:deep(.city-cascader-popper .el-cascader-node) {
  padding: 10px 20px;
  transition: background-color 0.2s ease, color 0.2s ease;
  font-size: 14px;
}

:deep(.city-cascader-popper .el-cascader-node:hover) {
  background-color: #f5f7fa;
}

:deep(.city-cascader-popper .el-cascader-node.is-active) {
  color: #409EFF;
  font-weight: bold;
  background-color: #ecf5ff;
}

.selected-cities-preview {
  margin-top: 10px;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 8px; /* Space between elements */
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  padding: 4px 0;
}

.selected-cities-preview .city-count {
  color: #409EFF;
  font-weight: 500;
}

.selected-cities-preview .city-names {
  flex-grow: 1; /* Allow names to take up available space */
  color: #606266;
  cursor: pointer;
  text-decoration: none; /* Remove default underline */
  border-bottom: 1px dotted #ccc; /* Dotted underline on hover */
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  white-space: nowrap; /* Prevent wrapping for names */
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected-cities-preview .city-names:hover {
  background-color: #f0f2f5;
  color: #409EFF;
  border-bottom-color: #409EFF;
}

.clear-cities-btn {
  margin-left: auto; /* Push to the right */
  border: none;
  background-color: transparent;
  color: #f56c6c; /* Red color for delete */
  transition: all 0.2s ease;
}

.clear-cities-btn:hover {
  background-color: #fef0f0; /* Light red background on hover */
  color: #f56c6c;
}

/* Service Category Select styles */
.selected-category-tag {
  margin-right: 5px;
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
  border-radius: 4px;
}

.category-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px; /* Padding inside options */
}

:deep(.category-select-dropdown) {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Rich Text Editor styles */
.editor-container {
  height: 300px; /* Fixed height for consistency */
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden; /* Ensure editor content stays within bounds */
  transition: border-color 0.3s;
}

.editor-container:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* Upload component styles */
.image-upload :deep(.el-upload--picture-card) {
  width: 140px; /* Slightly larger upload area */
  height: 140px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px; /* More rounded */
  transition: border-color 0.3s, background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 24px;
  color: #8c939d;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
  background-color: #f9f9f9;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 140px;
  height: 140px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.image-upload :deep(.el-upload__tip) {
  font-size: 13px; /* Smaller tip text */
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
  text-align: left; /* Align tip to the left */
}

/* Upload progress bar */
.upload-progress {
  margin-top: 15px;
  padding: 12px;
  background-color: #e6f7ff; /* Light blue background for progress */
  border-radius: 6px;
  border: 1px solid #91d5ff;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* Form actions (buttons) */
.form-actions {
  margin-top: 30px; /* More space above buttons */
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
}

/* Buttons */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 20px; /* Standard padding */
  font-size: 15px;
  transition: all 0.2s ease;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
}

:deep(.el-button:not(.el-button--primary):hover) {
  border-color: #c6e2ff;
  color: #409eff;
  background-color: #ecf5ff;
}

/* Responsive adjustments */
@media (max-width: 992px) { /* Medium devices (tablets) */
  .service-form-card {
    max-width: 760px;
    /* 在中等屏幕上保持边距 */
    margin: 0 16px;
  }
}

@media (max-width: 768px) { /* Small devices (phones) */
  .service-edit {
    /* 移动端保持无内边距 */
    padding: 0;
  }

  .page-main {
    /* 移动端保持无内边距 */
    padding: 0;
  }

  .service-form-card {
    /* 移动端移除圆角和阴影，但保留边框 */
    border-radius: 0;
    box-shadow: none;
    max-width: 100%;
    margin: 0;
    /* 保持顶部和底部的细边框作为分隔 */
    border-left: none;
    border-right: none;
  }

  .service-form {
    padding: 16px;
  }

  .service-form :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  .service-form :deep(.el-form-item__label) {
    line-height: 24px; /* Smaller line height for labels */
    padding-right: 0;
    text-align: left; /* Align labels to left on small screens */
  }

  .service-form :deep(.el-col) {
    margin-bottom: 10px; /* Add space between columns in a row */
  }

  .service-form :deep(.el-col:last-child) {
    margin-bottom: 0; /* No margin for the last column */
  }

  .form-actions {
    flex-direction: column; /* Stack buttons vertically */
    align-items: stretch; /* Stretch buttons to full width */
  }

  :deep(.el-button + .el-button) {
    margin-left: 0;
    margin-top: 10px; /* Space between stacked buttons */
  }

  .selected-cities-preview {
    flex-direction: column; /* Stack preview elements */
    align-items: flex-start;
  }

  .selected-cities-preview .clear-cities-btn {
    margin-left: 0; /* Remove auto margin */
    margin-top: 8px; /* Space above clear button */
  }
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  position: relative;
}

.form-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 30px;
  height: 2px;
  background: #409eff;
}

/* 页面主体样式 */
.service-edit {
  padding: 0;

  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 1200px;
  margin: 0;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.service-form {
  max-width: none;
}

.service-form .el-form-item {
  margin-bottom: 20px;
}

.service-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

/* 富文本编辑器样式优化 */
.editor-container {
  width: 100%;
}

/* 修复富文本编辑器字体选择下拉框样式 */
:deep(.edui-default .edui-toolbar .edui-combox .edui-combox-body) {
  height: auto !important;
  min-height: 20px !important;
}

:deep(.edui-default .edui-toolbar .edui-combox .edui-button-body) {
  height: 20px !important;
  line-height: 20px !important;
}

:deep(.edui-default .edui-toolbar .edui-combox .edui-combox-body .edui-arrow) {
  top: 50% !important;
  transform: translateY(-50%) !important;
}

:deep(.edui-default .edui-toolbar .edui-combox .edui-combox-body .edui-combox-text) {
  height: 20px !important;
  line-height: 20px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 富文本编辑器下拉菜单样式 */
:deep(.edui-default .edui-popup) {
  z-index: 9999 !important;
}

:deep(.edui-default .edui-popup .edui-popup-content) {
  max-height: 200px !important;
  overflow-y: auto !important;
}

:deep(.edui-default .edui-listitem) {
  height: auto !important;
  min-height: 24px !important;
  line-height: 24px !important;
  padding: 2px 8px !important;
}

:deep(.edui-default .edui-listitem:hover) {
  background-color: #f5f5f5 !important;
}
</style>